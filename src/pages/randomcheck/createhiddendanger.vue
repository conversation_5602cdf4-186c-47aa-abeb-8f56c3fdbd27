<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '隐患上报',
  },
}
</route>

<template>
  <SafetyNavbar
    @click-left="handleClickLeft"
    :title="detailInfo?.hazardRandomCheck?.checkName || '隐患上报'"
  ></SafetyNavbar>
  <view class="flex flex-col">
    <scroll-view style="height: 100vh" scroll-y="true">
      <view>
        <div v-for="(form, index) in modelArray" :key="index">
          <view class="form-title" style="">
            <view class="form-title-icon" v-if="index !== 0"></view>
            <view
              style="
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                width: 93%;
              "
            >
              <view style="margin-left: 4pt" v-if="index !== 0">隐患位置{{ index }}</view>
              <view v-if="index !== 0">
                <wd-button size="small" plain @click="delmodelArraybyindex(index)">删除</wd-button>
              </view>
            </view>
          </view>
          <wd-cell-group class="group" title="" border>
            <wd-form @click="getindex(index)" ref="form" :model="form">
              <!-- 隐患单位 -->
              <commonUnit
                @click="getindex(index)"
                v-if="hiddenType !== '0'"
                @getGrade="getUnit"
                :types="types"
                :sysCode="sysCode"
                :orgCode="topOrgCode"
                :selectvalue="form.unitId"
                :selecttext="form.unitName"
              ></commonUnit>
              <!-- 从隐患库中选择 -->
              <commonHazardDescribe
                @click="getindex(index)"
                v-if="pagetype === 0"
                :unitId="topUnitId"
              ></commonHazardDescribe>
              <view class="com-cell" v-if="pagetype === 0">
                <wd-textarea
                  type="textarea"
                  label-width="100px"
                  :maxlength="200"
                  show-word-limit
                  prop="remark"
                  clearable
                  auto-height
                  v-model="form.hazardDesc"
                  placeholder="请输入隐患描述"
                  @click="getindex(index)"
                />
              </view>
              <wd-textarea
                label="隐患描述"
                v-if="pagetype === 1"
                type="textarea"
                label-width="100px"
                prop="hazardDesc"
                clearable
                required
                :maxlength="200"
                show-word-limit
                v-model="form.hazardDesc"
                placeholder="请输入隐患描述"
                @click="getindex(index)"
              />
              <commonEssentialFactorNew
                @click="getindex(index)"
                :treeData="EssentialFactor"
                @sendEssen="sendEssen"
                :mrhazardType="form.hazardTypeName"
                :mrhazardTypeId="form.hazardType"
              ></commonEssentialFactorNew>
              <wd-select-picker
                @click="getindex(index)"
                :z-index="999"
                label="隐患等级"
                placeholder="请选择隐患等级"
                v-model="form.hazardLevel"
                :columns="gradeList"
                value-key="id"
                label-key="gradeName"
                align-right
                type="radio"
                required
                @confirm="handleConfirm1"
              ></wd-select-picker>

              <!-- 人工上报时展示 -->
              <comHazardSource
                v-if="types === '1'"
                title="隐患来源"
                @senddate="handleConfirm2"
              ></comHazardSource>

              <wd-cell
                title="楼栋楼层"
                title-width="100px"
                class="text"
                style="margin-left: 10rpx"
                :value="form.morehazardPlace"
                is-link
                @click="handleposition(index)"
                clickable
              />
              <wd-cell-group border>
                <div style="display: flex">
                  <div style="padding-left: 15px; color: red"></div>
                  <div style="padding-left: 5px; font-size: 14px">隐患位置</div>
                </div>
                <wd-input
                  style="padding-left: 20px; padding-right: 20px"
                  @click="getindex(index)"
                  label-width="100px"
                  prop="hazardDesc"
                  :maxlength="50"
                  show-word-limit
                  v-model="form.hazardPosition"
                  placeholder="请描述隐患详细位置"
                ></wd-input>
              </wd-cell-group>
              <wd-cell title="隐患图片" required />
              <view>
                <uploadButton
                  @click="getindex(index)"
                  ref="uploadButtonRef"
                  :imginfo="form.files"
                  @getFileObjList="getFilelist"
                ></uploadButton>
              </view>
              <commonSelectpicker
                @click="getindex(index)"
                :title="'隐患整改人员'"
                :orgUnitId="form.unitId"
                :orgname="form.unitName || userinfo.unitName"
                :outUnitid="form.unitId"
                :status="1"
                @send="reception"
              ></commonSelectpicker>
              <view>
                <wd-datetime-picker
                  label="隐患整改期限"
                  type="date"
                  v-model="pickerDate"
                  required
                  :minDate="Date.now()"
                  @confirm="confirmDate($event, index)"
                ></wd-datetime-picker>
                <view class="deadline">
                  <text class="text-[#666] text-[12px] flex-shrink-0">快捷选项：</text>
                  <wd-radio-group
                    shape="button"
                    v-model="deadDate"
                    size="small"
                    @change="confirmShort($event, index)"
                    class="flex-1 flex justify-around"
                  >
                    <wd-radio :value="7">一周</wd-radio>
                    <wd-radio :value="14">两周</wd-radio>
                    <wd-radio :value="30">一个月</wd-radio>
                    <wd-radio :value="60">两个月</wd-radio>
                  </wd-radio-group>
                </view>
              </view>

              <wd-cell-group border>
                <wd-textarea
                  @click="getindex(index)"
                  label="备注"
                  type="textarea"
                  label-width="100px"
                  :maxlength="500"
                  show-word-limit
                  prop="remark"
                  clearable
                  v-model="form.remark"
                  placeholder="此处填写隐患相关备注的内容"
                />
              </wd-cell-group>
            </wd-form>
          </wd-cell-group>
        </div>
        <!--        <view class="add-hazard" @click="addfrom">新增隐患</view>-->
      </view>
      <info1 v-if="pagetype === 1" :info1Itme="info"></info1>
      <view class="container-placeholder"></view>
      <div class="fixed-bottom">
        <wd-button type="info" size="large" @click="goBack" block>取消</wd-button>
        <wd-button type="primary" size="large" :loading="loading" @click="handleSubmit" block>
          保存
        </wd-button>
      </div>
    </scroll-view>
  </view>
  <!-- <div class="fixed-bottom">
    <wd-button type="info" size="large" @click="goBack" block>取消</wd-button>
    <wd-button type="primary" size="large" :loading="loading" @click="handleSubmit" block>
      保存
    </wd-button>
  </div> -->
</template>

<script lang="ts" setup>
import comHazardSource from '@/components/commonSelect/com-hazardSource.vue'
import commonEssentialFactorNew from '@/components/commonSelect/common-EssentialFactorNew.vue'
import commonHazardDescribe from '@/components/commonSelect/common-hazardDescribe.vue'
import commonSelectpicker from '@/components/commonSelect/common-selectpicker-new.vue'
import commonUnit from '@/components/commonSelect/common-Unit.vue'
import { posthazardGradeAPI } from '@/components/commonSelect/featch'
import uploadButton from '@/components/upload/upload-button.vue'
import { taskDetail } from '@/pages/task/featch'
import { useUserStore, usefileConfigStore } from '@/store'
import { debounce } from '@/utils'
import dayjs from 'dayjs'
import { ref } from 'vue'
import SafetyNavbar from '../components/safety-navbar.vue'
import info1 from '../task/components/info1.vue'
import {
  inspectDetail,
  posthazardEssentialFactorClassAPI,
  posthazardPlanTaskEventaddAPI,
  posthazardRandomCheckDetailAPI,
  posthazardRecordAddEventAPI,
  postTopLevelOrgCodeAPI,
} from './featch'
import { addForms } from './type'

const pickerDate = ref<number>(dayjs().valueOf()) // timestamp
const deadDate = ref<number | undefined>(undefined) // timestamp

const idreadonly = ref(true)
const loading = ref(false)
const userStore = useUserStore()
const userinfo: any =
  userStore.userInfo && Object.keys(userStore.userInfo).length > 5
    ? userStore.userInfo
    : uni.getStorageSync('userInfoObj')
// console.log(userinfo, '======userinfo3333=======')
const info = ref<any>({})
const pagetype = ref(0) // 页面输入框显示判断
const checkId = ref<any>('')
const taskId = ref<any>('')
const pointId = ref<any>('')
const apitype = ref<any>('') // 请求接口判断
const hiddenType = ref<any>('') // 判断是否从随机检查入口进入
const getplanId = ref<any>('')
const classItemId = ref<any>('') // 隐患检查项ID
const modelArray = ref<addForms<any>[]>([
  {
    typePid: '',
    buildingId: '',
    createBy: userinfo.id,
    createTime: '',
    disposeId: '',
    disposeState: '0',
    disposeStateName: '待整改',
    endTime: '',
    essentialFactorClassItemId: '', // 检查项ID----从隐患库中选择--检查表id
    checkAreaEssentialFactorId: '', // 检查项--检查表id---非快捷，不从隐患库选--从任务中获取检查表id
    eventTime: '',
    files: [],
    floorId: '',
    hazardDesc: '',
    hazardLevel: '',
    hazardLevelName: '',
    hazardPosition: '',
    hazardSource: 2, // 检查id为空 类型为人工上报
    hazardSourceName: '',
    hazardType: null,
    hazardTypeName: '',
    latitude: 0,
    longitude: 0,
    mapX: 0,
    mapY: 0,
    mapZ: 0,
    randomCheckId: checkId.value,
    reformUserJson: [],
    remark: '',
    search: '',
    startTime: '',
    superviseUnitId: userinfo.deptId,
    superviseUnitName: userinfo.deptName,
    unitId: '',
    unitName: '',
    updateBy: '',
    zhId: userinfo.zhId,
    timeoutDays: 0,
    id: '',
    morehazardPlace: '',
    planId: '',
    createByName: userinfo.userName,
    taskId: '',
    pointId: pointId.value || '',
    hazardRandomCheckEventUsers: [],
    hazardSourceNamevalue: '',
    correctionTime: dayjs().format('YYYY-MM-DD'), // 整改期限
  },
])
const uploadButtonRef = ref()

const topUnitId = ref<any>('') // 从详情获取topUnitId，用于查询隐患库

const FactorClassFullName = ref('') // 从隐患库中选择的分类

const sysCode = ref('')
const types = ref<string>('')
const disabled = ref(true)

// 显示头部
const detailInfo = ref()
const getInfo = async (id: string) => {
  const { data } = await posthazardRandomCheckDetailAPI(id)
  detailInfo.value = data
  // console.log(detailInfo)
}

const topOrgCode = ref('') // 隐患单位树 接口参数
onLoad(async (params) => {
  console.log(params, 'params')
  /*
   * @description 作业过来的query
   * sysCode 作业sysCode  -  safe-operation_app
   * workId 作业主键ID  -  2ff5c71199c047e885a45a6fe85c82c3
   * unitId 业主方单位id  -  7f0624b79f8940bb98d6a0340bcb1a10
   * unitName 业主方单位
   * */
  if (params.sysCode === 'safe-operation_app') {
    console.log('🚀 ~ onLoad ~:', params)
    const storedUserInfo = uni.getStorageSync('userInfoObj')
    console.log('storedUserInfo', storedUserInfo)
    try {
      const fileConfigStore = usefileConfigStore()
      // 尝试从本地存储获取已有的用户信息id
      if (storedUserInfo && storedUserInfo.id) {
        // 调用checkSysPower获取token
        let token = ''
        const respone = await userStore.checkSysPower({
          sysCode: 'hazard_inves_app',
          userId: storedUserInfo.id,
        })
        console.log('respone', respone)
        if (respone.code === 'success') {
          token = respone.data.token
        }
        const _res = await userStore.getUserInfo({ sysCode: 'hazard_inves_app', token })
        if (+_res.code === 200) {
          userStore.setUserInfo(_res.data)
          const $data = await fileConfigStore.getfileConfig()
          fileConfigStore.setfileConfig($data.data)
        } else {
          console.log('Token可能已过期，需要重新登录')
        }
      }
    } catch (error) {
      console.log('safe-operation_app场景用户信息获取失败:', error)
    }
    topOrgCode.value = params.unitId
    modelArray.value[0].unitId = params.unitId
    modelArray.value[0].unitName = params.unitName
    modelArray.value[0].floorId = params.floorId
    modelArray.value[0].buildingId = params.buildingId
    modelArray.value[0].appBizType = 1
    modelArray.value[0].appBizId = params.workId
    modelArray.value[0].morehazardPlace = params.applyPersonName

    modelArray.value[0].reformUserJson = params.personList
      ? JSON.parse(params.personList).map((item) => {
          return {
            // ...item,
            reformType: 0,
            userMasterUnitId: item.unitId,
            userMasterUnitName: item.unitName,
            userUnitId: item.unitId,
            userUnitName: item.unitName,
            userName: item.name,
            reformUserId: item.id,
            reformUserName: item.name,
            usertype: 1,
          }
        })
      : []
    ;(modelArray.value[0].mapX = Number(params.x)), (modelArray.value[0].mapY = Number(params.y))
    if (uni.getStorageSync('@userInfo')) {
      const userInfo = uni.getStorageSync('@userInfo')
      topUnitId.value = +userInfo.orgRes === 1 ? userInfo.topUnitId : userInfo.serverUnitId
    }
    getGradeAndLeve(params.unitId)
  }

  hiddenType.value = params.hiddenType
  types.value = params.types || '1' // 1人工上报

  if (Object.keys(params).length !== 0) {
    // checkAreaEssentialFactorId====params.checkContentId  点击上报带过来 默认等于 检查项id
    classItemId.value = params.checkContentId
    taskId.value = params.taskId
    checkId.value = params.checkId
    pointId.value = params.pointId

    if (params.checkId) {
      setTimeout(async () => {
        await getInfo(params.checkId)
      })
    }
    if (params.pointId) {
      modelArray.value[0].pointId = params.pointId
    }
    if (params.unitId) {
      modelArray.value[0].unitId = params.unitId
      getGradeAndLeve(params.unitId)
    }
    if (types.value === '1') {
      // 人工上报
      modelArray.value[0].hazardSource = 2
      // unitOrgType---业务单位=1，orgRes---组织架构=1
      if (userinfo.orgRes === '1' && userinfo.unitOrgType === '1') {
        modelArray.value[0].unitId = userinfo.unitId
        modelArray.value[0].unitName = userinfo.unitName
        getGradeAndLeve(userinfo.unitId)
      }
      // modelArray.value.push(model.value)
    } else {
      modelArray.value[0].hazardSource = 21 // 计划检查

      taskDetail(taskId.value).then((res: any) => {
        if (res.code === 'success') {
          topUnitId.value = res.data.topUnitId
        }
      })
    }

    // 获取隐患上报来源
    if (params.hazardSource) {
      modelArray.value[0].hazardSource = params.hazardSource
    }

    if (params.sysCode) {
      sysCode.value = params.sysCode
    }

    // getplanId.value = taskId.value ? uni.getStorageSync('planId') : '' // 当任务ID有值时取计划ID 否者为空
    getplanId.value = uni.getStorageSync('planId') ? uni.getStorageSync('planId') : ''
    if (params.planId) {
      getplanId.value = params.planId
    }
    // 人工上报
    if (params.hazardSource === '9' || modelArray.value[0].hazardSource === 2) {
      modelArray.value[0].planId = ''
    } else {
      modelArray.value[0].planId = getplanId.value
    }

    modelArray.value[0].checkAreaEssentialFactorId = classItemId.value // 不从隐患库选，非快捷上报获取任务中的检查表id

    modelArray.value[0].taskId = taskId.value
    modelArray.value[0].randomCheckId = checkId.value

    const unitList = uni.getStorageSync('unitList') ? uni.getStorageSync('unitList') : []
    if (unitList.length > 0) {
      modelArray.value[0].unitId = unitList[0].id
      modelArray.value[0].unitName = unitList[0].unitName
      getGradeAndLeve(unitList[0].id)
    }
    if (params.FactorId) {
      pagetype.value = 1
      const paramsinfo = { FactorId: params.FactorId, id: params.checkId }
      getcheckinfo(paramsinfo)
      disabled.value = false
    } else {
      pagetype.value = 0
    }
  } else {
    apitype.value = '1'
    modelArray.value[0].hazardSource = 2
  }
})

const descmodel = ref<any>({})
function getcheckinfo(params) {
  inspectDetail(params)
    .then((res: any) => {
      info.value = res.data
      if (res.data.essentialFactorList.length > 0) {
        /* 隐患等级 */
        descmodel.value.defaulhazardLevel = res.data.essentialFactorList[0].essentialFactorGradeId
        descmodel.value.defaulhazardLevelName = res.data.essentialFactorList[0].essentialFactorGrade
        /* 隐患分类 */
        descmodel.value.defaulhazardTypeId = res.data.essentialFactorList[0].essentialFactorClassId
        descmodel.value.defaulhazardTypeName = res.data.essentialFactorList[0].essentialFactorClass

        FactorClassFullName.value = res.data.essentialFactorList[0].essentialFactorClass
        // 隐患描述
        descmodel.value.defauldesc = res.data.essentialFactorList[0].essentialFactorDescribe
        // 检查项id
        descmodel.value.essentialFactorClassItemId = res.data.essentialFactorList[0].checkAreaId
      }
      modelArray.value = [
        {
          typePid: '',
          buildingId: '',
          createBy: userinfo.id,
          createTime: '',
          disposeId: '',
          disposeState: '0',
          disposeStateName: '待整改',
          endTime: '',
          essentialFactorClassItemId: descmodel.value.essentialFactorClassItemId,
          checkAreaEssentialFactorId: classItemId.value,
          eventTime: '',
          files: [],
          floorId: '',
          hazardDesc: descmodel.value.defauldesc,
          hazardLevel: descmodel.value.defaulhazardLevel,
          hazardLevelName: descmodel.value.defaulhazardLevelName,
          hazardPosition: '',
          hazardSource: 2, // 检查id为空 类型为人工上报
          hazardSourceName: '',
          hazardType: descmodel.value.defaulhazardTypeId,
          hazardTypeName: descmodel.value.defaulhazardTypeName,
          latitude: 0,
          longitude: 0,
          mapX: 0,
          mapY: 0,
          mapZ: 0,
          randomCheckId: checkId.value,
          reformUserJson: [],
          remark: '',
          search: '',
          startTime: '',
          superviseUnitId: userinfo.deptId,
          superviseUnitName: userinfo.deptName,
          unitId: modelArray.value[0].unitId,
          unitName: modelArray.value[0].unitName,
          updateBy: '',
          zhId: userinfo.zhId,
          timeoutDays: 0,
          id: '',
          morehazardPlace: '',
          planId: getplanId.value,
          createByName: userinfo.userName,
          taskId: taskId.value,
          pointId: pointId.value,
          hazardRandomCheckEventUsers: [],
          hazardSourceNamevalue: '',
          correctionTime: dayjs().format('YYYY-MM-DD'), // 整改期限
        },
      ]
    })
    .finally(() => {
      console.log('finally')
    })
}
// 新增隐患
/* function addfrom() {
  modelArray.value.push({
    typePid: modelArray.value[0].typePid,
    buildingId: '',
    createBy: userinfo.id,
    createTime: '',
    disposeId: '',
    disposeState: '0',
    disposeStateName: '待整改',
    endTime: '',
    essentialFactorClassItemId: modelArray.value[0].essentialFactorClassItemId,
    checkAreaEssentialFactorId: modelArray.value[0].checkAreaEssentialFactorId,
    eventTime: '',
    files: [],
    floorId: '',
    hazardDesc: modelArray.value[0].hazardDesc,
    hazardLevel: modelArray.value[0].hazardLevel,
    hazardLevelName: '',
    hazardPosition: '',
    hazardSource: modelArray.value[0].hazardSource,
    hazardSourceName: '',
    hazardType: modelArray.value[0].hazardType,
    hazardTypeName: modelArray.value[0].hazardTypeName,
    latitude: 0,
    longitude: 0,
    mapX: 0,
    mapY: 0,
    mapZ: 0,
    randomCheckId: modelArray.value[0].randomCheckId,
    reformUserJson: [],
    remark: '',
    search: '',
    startTime: '',
    superviseUnitId: userinfo.deptId,
    superviseUnitName: userinfo.deptName,
    unitId: modelArray.value[0].unitId,
    unitName: modelArray.value[0].unitName,
    updateBy: '',
    zhId: userinfo.zhId,
    timeoutDays: 0,
    id: '',
    morehazardPlace: '',
    planId: getplanId.value,
    createByName: userinfo.userName,
    taskId: taskId.value,
    hazardRandomCheckEventUsers: [],
    hazardSourceNamevalue: '',
  })
} */
function sendEssen(node) {
  console.log(node, '获取到的值')
  modelArray.value[indexnum.value].hazardType = node.id
  modelArray.value[indexnum.value].hazardTypeName = node.className
}

// 调用获取隐患分类
const EssentialFactor = ref<any>([])
function gethazardEssentialFactorClass(unitIdval) {
  posthazardEssentialFactorClassAPI({ unitId: unitIdval, parentId: '0' })
    .then((res: any) => {
      EssentialFactor.value = res.data
    })
    .finally(() => {
      // console.log(111)
    })
}

// 隐患等级
const gradeList = ref<any>([])
function getgradeList(unitIdval) {
  // console.log(unitIdval, '================调用获取隐患等级')
  posthazardGradeAPI({ unitId: unitIdval })
    .then((res: any) => {
      gradeList.value = res.data
    })
    .finally(() => {
      // console.log(111)
    })
}

const indexnum: any = ref(0)
const BuildingObj = ref<Record<string, any>>({})
onShow(() => {
  if (uni.getStorageSync('checkFormIndex')) {
    indexnum.value = uni.getStorageSync('checkFormIndex')
  }
  if (modelArray.value.length > 0) {
    if (uni.getStorageSync('FactorClassItem')) {
      // 如果选择的隐患库中的检查项不一样，则重新赋值
      if (
        modelArray.value[indexnum.value].essentialFactorClassItemId !==
        uni.getStorageSync('FactorClassItem').essentialFactorClassItemId
      ) {
        // 隐患描述
        modelArray.value[indexnum.value].hazardDesc =
          uni.getStorageSync('FactorClassItem').hazardDescribe
        // 隐患库iD
        modelArray.value[indexnum.value].typePid =
          uni.getStorageSync('FactorClassItem').essentialFactorId
        disabled.value = false
        // 隐患分类iD
        modelArray.value[indexnum.value].hazardType =
          uni.getStorageSync('FactorClassItem').essentialFactorClassId

        modelArray.value[indexnum.value].hazardTypeName =
          uni.getStorageSync('FactorClassItem').essentialFactorClassName ||
          uni.getStorageSync('FactorClassItem').essentialFactorClassFullName

        // 隐患等级id
        modelArray.value[indexnum.value].hazardLevel =
          uni.getStorageSync('FactorClassItem').hazardGradeId
        // 从隐患库中带出隐患分类
        FactorClassFullName.value = uni.getStorageSync('FactorClassItem').essentialFactorClassName
      }
      modelArray.value[indexnum.value].essentialFactorClassItemId =
        uni.getStorageSync('FactorClassItem').essentialFactorClassItemId

      // 如果从隐患库中选择检查项ID了，任务默认检查表id就赋空 但是若是从扫码检查跳转的就不能赋空
      if (modelArray.value[indexnum.value].essentialFactorClassItemId && !classItemId.value) {
        modelArray.value[indexnum.value].checkAreaEssentialFactorId = ''
      }
    }
    if (uni.getStorageSync('Building') && uni.getStorageSync('BuildingFool')) {
      // 添加楼栋楼层id
      if (
        uni.getStorageSync('Building').value === BuildingObj.value?.value &&
        uni.getStorageSync('Building').parentId === BuildingObj.value?.parentId
      ) {
        // 什么都不做
      } else {
        modelArray.value[indexnum.value].floorId = uni.getStorageSync('Building').value
        modelArray.value[indexnum.value].buildingId = uni.getStorageSync('Building').parentId
        modelArray.value[indexnum.value].morehazardPlace =
          uni.getStorageSync('Building').parentName + uni.getStorageSync('Building').label
        // modelArray.value[indexnum.value].hazardPosition =
        //   uni.getStorageSync('Building').parentName + uni.getStorageSync('Building').label
        // modelArray.value[indexnum.value].mapX = null
        // modelArray.value[indexnum.value].mapY = null
        // uni.removeStorageSync('pointerinfo')
      }
      BuildingObj.value = uni.getStorageSync('Building')
    }
    if (uni.getStorageSync('pointerinfo')) {
      modelArray.value[indexnum.value].mapX = uni.getStorageSync('pointerinfo').x
      modelArray.value[indexnum.value].mapY = uni.getStorageSync('pointerinfo').y
    }
  }
})
/* 获取位置 */
function handleposition(index) {
  uni.setStorageSync('checkFormIndex', index)
  indexnum.value = index
  if (modelArray.value[indexnum.value].unitId) {
    uni.navigateTo({
      url: `/pages/hazardPhoto/common-position?selunitid=${
        modelArray.value[indexnum.value].unitId
      }`,
    })
  } else {
    uni.showToast({
      icon: 'none',
      title: '请先选择隐患单位',
    })
  }
}
// 获取图片列表
function getFilelist(event) {
  setTimeout(() => {
    console.log('============第' + uni.getStorageSync('checkFormIndex') + '条数据图片列表')
    indexnum.value = uni.getStorageSync('checkFormIndex')
    modelArray.value[indexnum.value].files = event
  }, 100)
}
// 选择隐患等级
function handleConfirm1({ value, selectedItems }) {
  modelArray.value[indexnum.value].hazardLevel = selectedItems.id
}

// 选择隐患来源
function handleConfirm2(data) {
  // types
  // 【隐患治理】随手拍 隐患来源 去掉内外部
  // modelArray.value[indexnum.value].hazardSourceName =
  //   `人工上报-${data.hazardSourceName}-${data.sourceType === '1' ? '内部隐患' : '外部隐患'}`
  modelArray.value[indexnum.value].hazardSourceName = `人工上报-${data.hazardSourceName}`
}

// 保存当前操作的第几条数据
const getindex = (index: number) => {
  uni.setStorageSync('checkFormIndex', index)
  localStorage.setItem('checkFormIndex', index.toString())
  indexnum.value = index
}
// 调用获取隐患等级和分类
function getGradeAndLeve(code) {
  idreadonly.value = false
  postTopLevelOrgCodeAPI({ orgCode: code })
    .then((_res: any) => {
      gethazardEssentialFactorClass(_res.data.orgCode)
      getgradeList(_res.data.orgCode)
    })
    .catch(() => {
      // console.log(111)
    })
}

// 获取隐患单位
const getUnit = (data) => {
  if (modelArray.value[indexnum.value].unitId !== data.id) {
    getGradeAndLeve(data.id)
    uni.removeStorageSync('Building')
    uni.removeStorageSync('pointerinfo')
    uni.removeStorageSync('BuildingFool')
    modelArray.value[indexnum.value].floorId = ''
    modelArray.value[indexnum.value].buildingId = ''
    modelArray.value[indexnum.value].hazardPosition = ''
    modelArray.value[indexnum.value].morehazardPlace = ''
    modelArray.value[indexnum.value].mapX = null
    modelArray.value[indexnum.value].mapY = null
  }
  modelArray.value[indexnum.value].unitId = data.id
  modelArray.value[indexnum.value].unitName = data.text
}
// 获取整改人
function reception(event) {
  console.log(event)
  modelArray.value[indexnum.value].reformUserJson = convertToreformUserJson(
    JSON.parse(JSON.stringify(event)),
  )
}

// 删除新增隐患
function delmodelArraybyindex(index) {
  modelArray.value.splice(index, 1)
  // 重新渲染页面
  modelArray.value = [...modelArray.value]
}
/* 提交按钮 */
const handleSubmit = debounce(() => {
  loading.value = true
  console.log(modelArray.value)
  // 非空判断======开始
  for (let i = 0; i < modelArray.value.length; i++) {
    if (!modelArray.value[i].unitId) {
      uni.showToast({
        icon: 'none',
        title: '请选隐患单位',
      })
      loading.value = false
      return
    }
    if (!modelArray.value[i].hazardDesc) {
      uni.showToast({
        icon: 'none',
        title: '请输入隐患描述',
      })
      loading.value = false
      return
    }
    if (pagetype.value === 0 || apitype.value !== '') {
      if (!modelArray.value[i].hazardType) {
        uni.showToast({
          icon: 'none',
          title: '请选择隐患分类',
        })
        loading.value = false
        return
      }
    }
    if (pagetype.value === 1) {
      if (!modelArray.value[i].hazardTypeName) {
        uni.showToast({
          icon: 'none',
          title: '请输入隐患分类',
        })
        loading.value = false
        return
      }
    }

    if (!modelArray.value[i].hazardLevel) {
      uni.showToast({
        icon: 'none',
        title: '请选择隐患等级',
      })
      loading.value = false
      return
    }
    if (types.value === '1') {
      if (!modelArray.value[i].hazardSourceName) {
        uni.showToast({
          icon: 'none',
          title: '请选择隐患来源',
        })
        loading.value = false
        return
      }
    }

    // if (!modelArray.value[i].morehazardPlace) {
    //   uni.showToast({
    //     icon: 'none',
    //     title: '请选择楼栋楼层',
    //   })
    //   loading.value = false
    //   return
    // }
    // if (!modelArray.value[i].hazardPosition) {
    //   uni.showToast({
    //     icon: 'none',
    //     title: '请输入隐患位置',
    //   })
    //   loading.value = false
    //   return
    // }
    if (modelArray.value[i].files.length === 0) {
      uni.showToast({
        icon: 'none',
        title: '请选择上传隐患图片',
      })
      loading.value = false
      return
    }
    if (modelArray.value[i].reformUserJson.length === 0) {
      uni.showToast({
        icon: 'none',
        title: '请选择隐患整改人员',
      })
      loading.value = false
      return
    }
    if (!modelArray.value[i].correctionTime) {
      uni.showToast({
        icon: 'none',
        title: '请选择隐患整改期限',
      })
      loading.value = false
      return
    }
    // if (!modelArray.value[i].mapX || !modelArray.value[i].mapY) {
    //   uni.showToast({
    //     icon: 'none',
    //     title: '请在楼栋楼层中选择点位',
    //   })
    //   loading.value = false
    //   return
    // }
  }
  if ((!checkId.value && !taskId.value) || apitype.value !== '') {
    // 隐患上报
    posthazardRecordAddEventAPI(JSON.parse(JSON.stringify(modelArray.value)))
      .then((res: any) => {
        uni.hideLoading()
        if (res.code === 'error') {
          uni.showToast({
            icon: 'none',
            title: res.message || '请求错误',
          })
          return
        }
        uni.removeStorageSync('FactorClassItem')
        uni.removeStorageSync('Building')
        uni.removeStorageSync('pointerinfo')
        uni.removeStorageSync('BuildingFool')
        uni.removeStorageSync('userInfoObj')
        uni.showToast({
          icon: 'none',
          title: '添加成功',
        })
        setTimeout(() => {
          goBack()
          // handleClickLeft()
        }, 1000)
      })
      .catch(() => {
        uni.showToast({
          icon: 'none',
          title: '网络错误',
        })
        loading.value = false
      })
      .finally(() => {
        loading.value = false
      })
  } else {
    // 任务隐患记录表
    // console.log('任务隐患记录表')
    posthazardPlanTaskEventaddAPI(JSON.parse(JSON.stringify(modelArray.value)))
      .then((res: any) => {
        uni.removeStorageSync('FactorClassItem')
        uni.removeStorageSync('Building')
        uni.removeStorageSync('pointerinfo')
        uni.removeStorageSync('BuildingFool')
        uni.removeStorageSync('userInfoObj')

        if (res.code === 'error') {
          uni.showToast({
            icon: 'none',
            title: res.message || '隐患上报失败，请重试',
          })
          console.error('隐患上报失败:', res.message)
          // 上报失败时不跳转，让用户可以重新尝试
          loading.value = false
          return
        } else {
          uni.showToast({
            icon: 'none',
            title: '隐患上报成功',
          })

          // 设置隐患上报成功标识，用于scanCodes页面读取
          uni.setStorageSync('hazardReportSuccess', {
            success: true,
            checkContentId: classItemId.value,
            timestamp: Date.now(),
          })

          console.log('隐患上报成功，checkContentId:', classItemId.value)

          setTimeout(() => {
            goBack()
          }, 1500)
        }
      })
      .catch((error) => {
        console.error('隐患上报网络错误:', error)
        uni.showToast({
          icon: 'none',
          title: '网络错误，请检查网络连接后重试',
        })
        loading.value = false
      })
      .finally(() => {
        loading.value = false
      })
  }
}, 500)

function convertToreformUserJson(items) {
  // console.log(items)
  return items.map((item) => ({
    reformUserName: item.userName || item.name,
    reformUserId: item.usertype === 2 ? item.loginUserId : item.id,
    reformType: 0,
    // 用户类型（1：组织内人员；2：承租方人 ;3-相关方)
    userType: item.usertype,
    // 相关方/承租方---字段名可能不一样,在选择人员模块添加好
    userUnitId: item.unitId,
    userUnitName: item.unitName,
    //  组织机构传unitId,unitName
    userMasterUnitId: item.usertype === 1 ? item.unitId : item.userMasterUnitId,
    userMasterUnitName: item.usertype === 1 ? item.unitName : item.userMasterUnitName,
  }))
}
function handleClickLeft() {
  if (sysCode.value) {
    uni.removeStorageSync('FactorClassItem')
    uni.removeStorageSync('Building')
    uni.removeStorageSync('pointerinfo')
    uni.removeStorageSync('BuildingFool')
    uni.removeStorageSync('userInfoObj')
    window.history.back()
  } else {
    uni.navigateBack()
  }
}

onUnload(() => {
  uni.removeStorageSync('FactorClassItem')
  uni.removeStorageSync('Building')
  uni.removeStorageSync('pointerinfo')
  uni.removeStorageSync('BuildingFool')
  // BuildingFool
})

// 确定时间
function confirmDate(e: { value: any }, index: number) {
  deadDate.value = undefined
  modelArray.value[index].correctionTime = dayjs(e.value).format('YYYY-MM-DD')
}
// 快捷选项
function confirmShort(e: { value: any }, index: number) {
  if (e.value) {
    pickerDate.value = dayjs().add(e.value, 'day').valueOf()
    modelArray.value[index].correctionTime = dayjs().add(e.value, 'day').format('YYYY-MM-DD')
  }
}
// // 取消上报
const goBack = () => {
  if (sysCode.value) {
    location.reload()
    window.history.back()
    // webUni.navigateBack()
  } else {
    uni.navigateBack()
  }
}
</script>

<style lang="scss">
::v-deep {
  .wd-input__label.is-required {
    padding-left: 7px;
  }

  .wd-cell__value {
    font-size: 0.8rem;
    color: #999999 !important;
  }

  .wd-button.is-block {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
  }

  .wd-cell__value {
    font-size: 0.8rem;
    color: #999999 !important;
  }

  .text .wd-cell__value {
    color: rgba($color: #000000, $alpha: 0.8) !important;
  }

  .wd-button.is-block {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
  }

  .wd-col-picker__cell.is-align-right .wd-col-picker__value {
    width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .wd-select-picker__value--placeholder {
    font-size: 13.312px;
  }
}

.FactorClassFullNamecalss {
  ::v-deep {
    .wd-select-picker__value--placeholder {
      color: black !important;
    }

    .wd-col-picker__value--placeholder {
      color: black !important;
    }
  }
}

.required::before {
  // position: absolute;
  // left: 0;
  top: 0.125rem;
  line-height: 1.1;
  color: #fa4350;
  content: '*';
}

.deadline {
  @apply flex justify-between items-center flex-wrap;
  margin: 0 1rem 0 1rem;

  :deep(.wd-radio.is-button .wd-radio__label) {
    height: 1.5rem;
    line-height: 1.5rem;
    padding: 0 0.8rem;
  }

  padding-bottom: 0.8rem;
  border-bottom: 1px solid #e5e7eb;
}

.com-cell {
  margin: 0 1rem 0 1rem;
  // border: 0.0187rem solid #ccc;
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 3.375rem;
  line-height: 3.375rem;
  color: #fff;
  text-align: center;
  background-color: white;
  z-index: 6;
}

.add-hazard {
  width: 96vw;
  height: 35px;
  margin: 10px auto 20px;
  line-height: 35px;
  color: #fff;
  text-align: center;
  background-color: #0256f7;
  border-radius: 6px;
}

.container-placeholder {
  width: 100%;
  height: 3.375rem;
}

.select-address {
  display: flex;
  justify-content: flex-end;
  padding-right: 10px;
  margin: 10px 0px;

  .select-address-button {
    padding: 8px 14px;
    color: #fff;
    background-color: #597bf7;
    border-radius: 10px;
  }
}
</style>
